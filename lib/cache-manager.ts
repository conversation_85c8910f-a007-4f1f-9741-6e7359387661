import NodeCache from "node-cache";
import { getPayloadClient } from "../server/get-payload";

// Environment-based cache TTL
const getCacheTTL = () => {
  // For development/testing: shorter TTL for quick updates
  if (process.env.NODE_ENV === 'development') {
    return 300; // 5 minutes
  }
  // For production: longer TTL for performance
  return 172800; // 48 hours
};

// Centralized cache instance
const cache = new NodeCache({
  stdTTL: getCacheTTL(),
  checkperiod: 120, // Check for expired keys every 2 minutes
  maxKeys: 1000,
});

// Cache key generator
const generateCacheKey = (collection: string, query: any = {}) => {
  const queryString = JSON.stringify(query);
  return `${collection}:${Buffer.from(queryString).toString('base64')}`;
};

// Get latest update timestamp for a collection
const getCollectionLastUpdated = async (collection: string) => {
  try {
    const payload = await getPayloadClient();
    const { docs } = await payload.find({
      collection: collection as any,
      limit: 1,
      sort: "-updatedAt",
    });
    return docs[0]?.updatedAt || new Date().toISOString();
  } catch (error) {
    console.error(`Error getting last updated for ${collection}:`, error);
    return new Date().toISOString();
  }
};

// Check if cache is valid by comparing with DB
const isCacheValid = async (cacheKey: string, collection: string) => {
  const cachedMeta = cache.get(`${cacheKey}:meta`) as { lastUpdated: string } | undefined;
  if (!cachedMeta) return false;

  const dbLastUpdated = await getCollectionLastUpdated(collection);
  return cachedMeta.lastUpdated === dbLastUpdated;
};

// Main cache function
export const cacheQuery = async <T>(
  collection: string,
  query: any = {},
  options: {
    ttl?: number;
    forceRefresh?: boolean;
    customCacheKey?: string;
  } = {}
): Promise<T> => {
  const { ttl = getCacheTTL(), forceRefresh = false, customCacheKey } = options;
  
  // Generate cache key
  const cacheKey = customCacheKey || generateCacheKey(collection, query);
  
  // Get cached data
  const cachedData = cache.get<T>(cacheKey);

  // Check cache validity against database (for distributed systems)
  if (!forceRefresh && cachedData) {
    const isValid = await isCacheValid(cacheKey, collection);
    if (isValid) {
      console.log(`✅ Serving from cache: ${collection}`);
      return cachedData;
    } else {
      console.log(`🔄 Cache invalid, fetching fresh data: ${collection}`);
      // Cache is invalid, continue to fetch fresh data
    }
  }

  // Fetch fresh data from database
  console.log(`🔄 Fetching fresh data: ${collection}`);
  const payload = await getPayloadClient();
  const result = await payload.find({
    collection: collection as any,
    ...query,
  });

  // Get current timestamp for cache validation
  const currentTimestamp = await getCollectionLastUpdated(collection);

  // Cache the result with metadata
  cache.set(cacheKey, result, ttl);
  cache.set(`${cacheKey}:meta`, { lastUpdated: currentTimestamp }, ttl);
  
  return result as T;
};

// Cache single document by ID
export const cacheDocument = async <T>(
  collection: string,
  id: string,
  options: {
    ttl?: number;
    forceRefresh?: boolean;
    depth?: number;
  } = {}
): Promise<T> => {
  const { ttl = getCacheTTL(), forceRefresh = false, depth = 1 } = options;

  const cacheKey = `${collection}:doc:${id}`;

  const cachedData = cache.get<T>(cacheKey);

  // Check cache validity against database
  if (!forceRefresh && cachedData) {
    const isValid = await isCacheValid(cacheKey, collection);
    if (isValid) {
      console.log(`✅ Serving document from cache: ${collection}/${id}`);
      return cachedData;
    } else {
      console.log(`🔄 Cache invalid, fetching fresh document: ${collection}/${id}`);
    }
  }

  console.log(`🔄 Fetching fresh document: ${collection}/${id}`);
  const payload = await getPayloadClient();
  const result = await payload.findByID({
    collection: collection as any,
    id,
    depth,
  });

  // Get current timestamp for cache validation
  const currentTimestamp = await getCollectionLastUpdated(collection);

  // Cache the result with metadata
  cache.set(cacheKey, result, ttl);
  cache.set(`${cacheKey}:meta`, { lastUpdated: currentTimestamp }, ttl);

  return result as T;
};

// Invalidate cache for a collection
export const invalidateCollectionCache = (collection: string) => {
  const keys = cache.keys();
  const collectionKeys = keys.filter(key => key.startsWith(`${collection}:`));

  collectionKeys.forEach(key => {
    cache.del(key);
    // Also delete meta cache
    cache.del(`${key}:meta`);
  });

  console.log(`🗑️ Invalidated cache for collection: ${collection} (${collectionKeys.length} keys)`);
};

// Invalidate specific cache key
export const invalidateCache = (cacheKey: string) => {
  cache.del(cacheKey);
  console.log(`🗑️ Invalidated cache key: ${cacheKey}`);
};

// Get cache stats
export const getCacheStats = () => {
  return {
    keys: cache.keys().length,
    stats: cache.getStats(),
  };
};

// Clear all cache
export const clearAllCache = () => {
  cache.flushAll();
  console.log("🗑️ Cleared all cache");
};

// Test cache invalidation
export const testCacheInvalidation = () => {
  console.log("🧪 Current cache keys:", cache.keys());
  console.log("🧪 Cache stats:", cache.getStats());
};

// Force refresh cache for testing
export const forceRefreshCache = async (collection: string) => {
  console.log(`🔄 Force refreshing cache for: ${collection}`);
  invalidateCollectionCache(collection);
  return `Cache cleared for ${collection}`;
};

// For distributed systems, consider using Redis instead of NodeCache
// Example Redis setup (uncomment when needed):
/*
import Redis from 'ioredis';
const redis = new Redis(process.env.REDIS_URL);

export const distributedCache = {
  get: async (key: string) => {
    const data = await redis.get(key);
    return data ? JSON.parse(data) : null;
  },
  set: async (key: string, value: any, ttl: number) => {
    await redis.setex(key, ttl, JSON.stringify(value));
  },
  del: async (key: string) => {
    await redis.del(key);
  }
};
*/

// Export cache instance for direct access if needed
export { cache };
